package com.bonc.rrs.serviceprovider.service.impl.business;

import cn.hutool.core.util.StrUtil;
import com.bonc.rrs.byd.domain.PushRepairOrderBody;
import com.bonc.rrs.byd.domain.PushRepairOrderData;
import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.byd.response.PushApiResponse;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.RegionCode;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInfoEntity;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.service.WorderTemplateService;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.constant.WarningConstant;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.BrandService;
import com.youngking.renrenwithactiviti.modules.sys.service.SysDictionaryDetailService;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 服务商业务-报修订单信息推送服务商
 * @Date 2024/2/26 18:04
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusinessProcessPushRepairOrder extends AbstractPushOrder {

    final BrandService brandService;

    final WorderTemplateService worderTemplateService;

    final SysDictionaryDetailService sysDictionaryDetailService;

    // 售后类型常量
    // 报修
    private static final String REPAIR_TYPE = "10";
    // 拆桩
    private static final String SPLIT_TYPE = "20";
    // 移桩
    private static final String MOVE_TYPE = "30";

    // 移桩-拆桩和安装标志常量
    // 拆桩和安装
    private static final String SPLIT_AND_INSTALL = "1";
    // 拆桩
    private static final String SPLIT_ONLY = "2";
    // 安装
    private static final String INSTALL_ONLY = "3";


    @Override
    public String getProcessCode() {
        return "pushRepairOrder";
    }

    @Override
    public Result callBusinessProcess(BusinessProcessPo businessProcessPo) {
        Integer worderTypeId = businessProcessPo.getWorderTypeId();
        PushRepairOrderBody pushRepairOrderBody = businessProcessPo.getPushRepairOrderBody();
        List<PushRepairOrderData> originalData = pushRepairOrderBody.getData();

        // 预处理数据：对移桩且拆桩和安装的订单进行拆分
        List<PushRepairOrderData> processedData = preprocessData(originalData);

        List<Object> errorDataList = new ArrayList<>();
        for (PushRepairOrderData pushRepairOrderData : processedData) {
            log.info("pushRepairOrder {} start", pushRepairOrderData.getOrderCode());
            if (processSingleOrder(businessProcessPo, pushRepairOrderData, errorDataList, worderTypeId)) {
                continue;
            }
            log.info("pushRepairOrder {} end", pushRepairOrderData.getOrderCode());
        }

        PushApiResponse pushApiResponse;
        if (!errorDataList.isEmpty()) {
            pushApiResponse = new PushApiResponse("partially success");
            pushApiResponse.setData(errorDataList);
        } else {
            pushApiResponse = new PushApiResponse("success");
        }

        return Result.pushApiResponse(pushApiResponse);
    }

    /**
     * 预处理数据：对移桩且拆桩和安装的订单进行拆分
     * @param originalData 原始数据列表
     * @return 处理后的数据列表
     */
    private List<PushRepairOrderData> preprocessData(List<PushRepairOrderData> originalData) {
        List<PushRepairOrderData> processedData = new ArrayList<>();

        for (PushRepairOrderData data : originalData) {
            // 检查是否为移桩且拆桩和安装的订单
            if (MOVE_TYPE.equals(data.getRequirmentType()) &&
                SPLIT_AND_INSTALL.equals(data.getTransferSplitInstallFlag())) {

                log.info("检测到移桩拆桩和安装订单，开始拆分：{}", data.getOrderCode());

                // 验证移桩地址数据完整性
                if (validateMoveOrderData(data)) {
                    List<PushRepairOrderData> splitOrders = splitMoveOrder(data);
                    processedData.addAll(splitOrders);
                    log.info("订单拆分完成：{} -> {} 和 {}",
                            data.getOrderCode(),
                            splitOrders.get(0).getOrderCode(),
                            splitOrders.get(1).getOrderCode());
                } else {
                    log.warn("移桩订单数据不完整，跳过拆分：{}", data.getOrderCode());
                    processedData.add(data);
                }
            } else {
                processedData.add(data);
            }
        }

        return processedData;
    }

    /**
     * 验证移桩订单数据完整性
     * @param data 订单数据
     * @return 是否有效
     */
    private boolean validateMoveOrderData(PushRepairOrderData data) {
        return StrUtil.isNotBlank(data.getTransferProvinceCode()) &&
               StrUtil.isNotBlank(data.getTransferCityCode()) &&
               StrUtil.isNotBlank(data.getTransferAreaCode()) &&
               StrUtil.isNotBlank(data.getTransferAddress());
    }

    /**
     * 拆分移桩订单为拆桩和安装两个订单
     * @param originalData 原始移桩订单数据
     * @return 拆分后的订单列表（拆桩订单和安装订单）
     */
    private List<PushRepairOrderData> splitMoveOrder(PushRepairOrderData originalData) {
        List<PushRepairOrderData> splitOrders = new ArrayList<>();

        // 创建拆桩订单
        PushRepairOrderData dismantleOrder = createSplitOrder(originalData);
        splitOrders.add(dismantleOrder);

        // 创建安装订单
        PushRepairOrderData installOrder = createInstallOrder(originalData);
        splitOrders.add(installOrder);

        return splitOrders;
    }

    /**
     * 创建拆桩订单
     * @param originalData 原始数据
     * @return 拆桩订单数据
     */
    private PushRepairOrderData createSplitOrder(PushRepairOrderData originalData) {
        PushRepairOrderData splitOrder = copyOrderData(originalData);

        // 设置拆桩特有属性
        splitOrder.setOrderCode(originalData.getOrderCode() + ConstantPool.SPLIT_SUFFIX);
        // 拆桩使用原地址，不需要修改地址相关字段
        return splitOrder;
    }

    /**
     * 创建安装订单
     * @param originalData 原始数据
     * @return 安装订单数据
     */
    private PushRepairOrderData createInstallOrder(PushRepairOrderData originalData) {
        PushRepairOrderData installOrder = copyOrderData(originalData);

        // 设置安装特有属性
        installOrder.setOrderCode(originalData.getOrderCode() + ConstantPool.INSTALL_SUFFIX);

        // 安装使用移桩地址
        installOrder.setProvinceCode(originalData.getTransferProvinceCode());
        installOrder.setProvinceName(originalData.getTransferProvince());
        installOrder.setCityCode(originalData.getTransferCityCode());
        installOrder.setCityName(originalData.getTransferCity());
        installOrder.setAreaCode(originalData.getTransferAreaCode());
        installOrder.setAreaName(originalData.getTransferArea());
        installOrder.setContactAddress(originalData.getTransferAddress());

        return installOrder;
    }

    /**
     * 复制订单数据
     * @param original 原始数据
     * @return 复制的数据
     */
    private PushRepairOrderData copyOrderData(PushRepairOrderData original) {
        PushRepairOrderData copy = new PushRepairOrderData();

        // 复制所有字段
        copy.setInstallOrderCode(original.getInstallOrderCode());
        copy.setContactName(original.getContactName());
        copy.setContactMobile(original.getContactMobile());
        copy.setContactAddress(original.getContactAddress());
        copy.setProvinceCode(original.getProvinceCode());
        copy.setProvinceName(original.getProvinceName());
        copy.setCityCode(original.getCityCode());
        copy.setCityName(original.getCityName());
        copy.setAreaCode(original.getAreaCode());
        copy.setAreaName(original.getAreaName());
        copy.setWallboxName(original.getWallboxName());
        copy.setWallboxPower(original.getWallboxPower());
        copy.setRequirmentType(original.getRequirmentType());
        copy.setDispatchTime(original.getDispatchTime());
        copy.setType(original.getType());
        copy.setCarBrand(original.getCarBrand());
        copy.setCarSeries(original.getCarSeries());
        copy.setCarModel(original.getCarModel());
        copy.setVin(original.getVin());
        copy.setRemark(original.getRemark());
        // 这个会在具体的create方法中被修改
        copy.setOrderCode(original.getOrderCode());
        copy.setTransferProvinceCode(original.getTransferProvinceCode());
        copy.setTransferProvince(original.getTransferProvince());
        copy.setTransferCityCode(original.getTransferCityCode());
        copy.setTransferCity(original.getTransferCity());
        copy.setTransferAreaCode(original.getTransferAreaCode());
        copy.setTransferArea(original.getTransferArea());
        copy.setTransferAddress(original.getTransferAddress());
        copy.setGlobalGuarantee(original.getGlobalGuarantee());
        copy.setMaintenanceStatus(original.getMaintenanceStatus());
        copy.setTransferSplitInstallFlag(original.getTransferSplitInstallFlag());

        return copy;
    }

    private boolean processSingleOrder(BusinessProcessPo businessProcessPo, PushRepairOrderData pushRepairOrderData, List<Object> errorDataList, Integer worderTypeId) {
        R r = null;
        try {
            BrandEntity brandEntity = brandService.queryBrandByCompanyBrandId(pushRepairOrderData.getCarBrand());
            if (brandEntity == null) {
                log.info("pushRepairOrder " + pushRepairOrderData.getOrderCode() + " 非法品牌");
                addError(pushRepairOrderData.getOrderCode(), "非法品牌", errorDataList);
                return true;
            }

            RegionCode regionCode = convertRegion(pushRepairOrderData.getProvinceCode(), pushRepairOrderData.getCityCode(), pushRepairOrderData.getAreaCode());

            if (regionCode.getProvinceCode() == null || regionCode.getCityCode() == null || regionCode.getAreaCode() == null) {
                log.info("pushRepairOrder " + pushRepairOrderData.getOrderCode() + " 地区不匹配");
                addError(pushRepairOrderData.getOrderCode(), "地区不匹配", errorDataList);
                return true;
            }

            WorderTemplateDto worderTemplateDto = null;

            List<WorderTemplateDto> worderTemplateDtoList = worderTemplateService.findTemplateInfoByBrandIdAndWorderTypeIdAndRegion(brandEntity.getId(), worderTypeId, regionCode.getProvinceCode().intValue(), regionCode.getCityCode().intValue());
            if (CollectionUtils.isEmpty(worderTemplateDtoList)) {
                log.info("pushRepairOrder " + pushRepairOrderData.getOrderCode() + " 没有对应的工单模板");
                addError(pushRepairOrderData.getOrderCode(), "没有对应的工单模板", errorDataList);
                return true;
            }
            worderTemplateDtoList.sort(Comparator.comparing(WorderTemplateDto::getTemplateId).reversed());
            // 售后类型 10-报修，20-拆桩，30-移桩
            String requirmentType = pushRepairOrderData.getRequirmentType();
            String transferSplitInstallFlagName = null;

            // 根据售后类型选择对应的工单模板
            if (REPAIR_TYPE.equals(requirmentType)) {
                // 报修取最新的模版
                worderTemplateDto = worderTemplateDtoList.stream()
                        .filter(template -> StrUtil.containsAnyIgnoreCase(template.getTemplateName(), "维修"))
                        .findFirst().orElse(null);
            } else if (SPLIT_TYPE.equals(requirmentType)) {
                // 根据品牌获取对应的拆桩模版
                worderTemplateDto = worderTemplateDtoList.stream()
                        .filter(template -> StrUtil.containsAnyIgnoreCase(template.getTemplateName(), "移桩&拆桩"))
                        .findFirst().orElse(null);
            } else if (MOVE_TYPE.equals(requirmentType)) {
                // 售后类型为移桩时，移桩-拆桩和安装标志必填
                String transferSplitInstallFlag = pushRepairOrderData.getTransferSplitInstallFlag();
                if (transferSplitInstallFlag == null) {
                    log.info("pushRepairOrder {} 移桩-拆桩和安装标志不能为空", pushRepairOrderData.getOrderCode());
                    addError(pushRepairOrderData.getOrderCode(), "移桩-拆桩和安装标志不能为空", errorDataList);
                    return true;
                }

                switch (transferSplitInstallFlag) {
                    case SPLIT_AND_INSTALL:
                        // 拆桩和安装（注意：这种情况在预处理阶段已经被拆分，理论上不应该到达这里）
                        transferSplitInstallFlagName = "拆桩和安装";
                        if (StrUtil.endWithIgnoreCase(pushRepairOrderData.getOrderCode(), ConstantPool.SPLIT_SUFFIX)) {
                            worderTemplateDto = worderTemplateDtoList.stream()
                                    .filter(template -> StrUtil.containsAnyIgnoreCase(template.getTemplateName(), "移桩&拆桩"))
                                    .findFirst().orElse(null);
                        } else if (StrUtil.endWithIgnoreCase(pushRepairOrderData.getOrderCode(), ConstantPool.INSTALL_SUFFIX)) {
                            worderTemplateDto = worderTemplateDtoList.stream()
                                    .filter(template -> StrUtil.containsAnyIgnoreCase(template.getTemplateName(), "移桩&安装"))
                                    .findFirst().orElse(null);
                        }

                        break;
                    case SPLIT_ONLY:
                        // 拆桩
                        transferSplitInstallFlagName = "拆桩";
                        worderTemplateDto = worderTemplateDtoList.stream()
                                .filter(template -> StrUtil.containsAnyIgnoreCase(template.getTemplateName(), "移桩&拆桩"))
                                .findFirst().orElse(null);
                        break;
                    case INSTALL_ONLY:
                        // 安装
                        transferSplitInstallFlagName = "安装";
                        worderTemplateDto = worderTemplateDtoList.stream()
                                .filter(template -> StrUtil.containsAnyIgnoreCase(template.getTemplateName(), "移桩&安装"))
                                .findFirst().orElse(null);
                        break;
                    default:
                        log.info("pushRepairOrder {} 移桩-拆桩和安装标志不正确", pushRepairOrderData.getOrderCode());
                        addError(pushRepairOrderData.getOrderCode(), "移桩-拆桩和安装标志不正确", errorDataList);
                        return true;
                }
            }
            if (worderTemplateDto == null) {
                log.info("pushRepairOrder " + pushRepairOrderData.getOrderCode() + " 获取工单模板失败");
                addError(pushRepairOrderData.getOrderCode(), "获取工单模板失败", errorDataList);
                return true;
            }

            if (validateCompanyOrderNumberAndBrandExsit(pushRepairOrderData.getOrderCode(), worderTemplateDto.getTemplateId())) {
                log.info("pushRepairOrder " + pushRepairOrderData.getOrderCode() + " 车企订单号已存在，无法创建报修工单");
//                    addError(pushRepairOrderData.getOrderCode(), "车企订单号已存在，无法创建报修工单", errorDataList);
                return true;
            }

            Integer companyId = 516;

            WorderInfoEntity worderInfoEntity = new WorderInfoEntity();

            String address = buildAddress(regionCode, pushRepairOrderData.getContactAddress());

            String installOrderCode = pushRepairOrderData.getInstallOrderCode();
            String wallboxName = pushRepairOrderData.getWallboxName();
            String wallboxPower = pushRepairOrderData.getWallboxPower();

            String requirmentTypeName = sysDictionaryDetailService.getDetailName("byd_requirment_type", requirmentType);
            String dispatchTime = pushRepairOrderData.getDispatchTime();

            String orderTag = sysDictionaryDetailService.getDetailName("byd_order_tag", pushRepairOrderData.getType());

            //桩体标签 pileBodyType 10-桩体保内，20-桩体保外，30-桩体延保
            String  pileBodyType = BusinessProcessPushRepairOrder.PileBodyTypeEnum.getValueByCode(pushRepairOrderData.getPileBodyType());

            String bydCarSeries = pushRepairOrderData.getCarSeries();
            String bydCarModel = pushRepairOrderData.getCarModel();

            String vin = pushRepairOrderData.getVin();
            String remark = pushRepairOrderData.getRemark();
            String orderCode = pushRepairOrderData.getOrderCode();

            //是否全国联保订单: 0-否 1-是
            String isCountryWarranty = pushRepairOrderData.getGlobalGuarantee();
            //是否可维护: 0-否 1-是
            String isMaintainable = pushRepairOrderData.getMaintenanceStatus();

            worderInfoEntity.setIsMaintainable(YesOrNo.getValueByCode(isMaintainable));

            worderInfoEntity.setPushOrderWorderSource(businessProcessPo.getOrderSouce());
            worderInfoEntity.setUserName(pushRepairOrderData.getContactName());
            worderInfoEntity.setUserPhone(pushRepairOrderData.getContactMobile());
            worderInfoEntity.setAddress(address);
            worderInfoEntity.setCompanyOrderNumber(orderCode);
            worderInfoEntity.setTemplateId(worderTemplateDto.getTemplateId());

            worderInfoEntity.setCarBrand(brandEntity.getId() + "");
            worderInfoEntity.setCarModel("4");
            worderInfoEntity.setCompanyId(companyId);

            worderInfoEntity.setPostcode("");
            worderInfoEntity.setWorderSourceTypeValue("");
            worderInfoEntity.setWorderTypeId(worderTypeId);

            worderInfoEntity.setCandidate(ConstantPool.NEWS_OPERATOR_NAME);
            worderInfoEntity.setCreator(ConstantPool.NEWS_OPERATOR);

            List<WorderExtFieldEntity> worderExtFieldEntityList = new ArrayList<>();

            worderExtFieldEntityList.add(setWorderExtFieldEntity(1, "工单类型", worderInfoEntity.getWorderTypeId()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(5, "车企订单号", worderInfoEntity.getCompanyOrderNumber()));

            worderExtFieldEntityList.add(setWorderExtFieldEntity(101, "车企名称", companyId));

            worderExtFieldEntityList.add(setWorderExtFieldEntity(154, "车企派单日期", dispatchTime));

            worderExtFieldEntityList.add(setWorderExtFieldEntity(147, "安装单号", installOrderCode));

            worderExtFieldEntityList.add(setWorderExtFieldEntity(306, "工单来源", ""));

            worderExtFieldEntityList.add(setWorderExtFieldEntity(902, "客户姓名", worderInfoEntity.getUserName()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(903, "安装地址", address));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(904, "客户邮箱", ""));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(905, "客户手机", worderInfoEntity.getUserPhone()));

            worderExtFieldEntityList.add(setWorderExtFieldEntity(1676, "充电桩名称", wallboxName));

            worderExtFieldEntityList.add(setWorderExtFieldEntity(1682, "比亚迪-品牌", brandEntity.getBrandName()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(1684, "比亚迪车系", bydCarSeries));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(1686, "比亚迪车型", bydCarModel));

            worderExtFieldEntityList.add(setWorderExtFieldEntity(1694, "充电桩功率", wallboxPower));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(1697, "售后类型", requirmentTypeName));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(1698, "标签", orderTag));
            //TODO 桩体标签 pileBodyType 10-桩体保内，20-桩体保外，30-桩体延保
            worderExtFieldEntityList.add(setWorderExtFieldEntity(1699, "桩体标签", pileBodyType));

            worderExtFieldEntityList.add(setWorderExtFieldEntity(1705, "车架号", vin));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(1707, "故障原因", remark));

            //TODO 是否全国联保订单: 0-否 1-是
            worderExtFieldEntityList.add(setWorderExtFieldEntity(2097, "是否全国联保订单", YesOrNo.getValueByCode(isCountryWarranty)));
            //TODO 是否可维护: 0-否 1-是
            worderExtFieldEntityList.add(setWorderExtFieldEntity(2098, "是否可维护", YesOrNo.getValueByCode(isMaintainable)));
            //TODO 移桩-拆桩和安装标志
            worderExtFieldEntityList.add(setWorderExtFieldEntity(2259, "移桩-拆桩和安装标志", transferSplitInstallFlagName));

            worderInfoEntity.setWorderExtFieldList(worderExtFieldEntityList);

            r = saveWorderInformation(worderInfoEntity);

        } catch (Exception e) {
            log.error("pushRepairOrder " + pushRepairOrderData.getOrderCode() + " 出现异常", e);
            addError(pushRepairOrderData.getOrderCode(), "下单失败，" + e.toString(), errorDataList);
            return true;
        }
        if(!IntegerEnum.ZERO.getValue().equals(r.get(WarningConstant.CODE))){
            log.info("pushRepairOrder " + pushRepairOrderData.getOrderCode() + r.get("msg"));
            addError(pushRepairOrderData.getOrderCode(), r.get("msg") + "", errorDataList);
            return true;
        }
        String worderNo = r.get("worderNo") + "";
        // 自动派单
        goAutoSendWorder(worderNo, ConstantPool.NEWS_OPERATOR_NAME);
        return false;
    }

    /**
     * 构建地址字符串
     * @param regionCode 区域编码
     * @param detailAddress 详细地址
     * @return 完整地址字符串
     */
    private String buildAddress(RegionCode regionCode, String detailAddress) {
        return regionCode.getProvinceCode() + "_" +
               regionCode.getCityCode() + "_" +
               regionCode.getAreaCode() + "_" +
               detailAddress;
    }

    //创建枚举类：是--1，否--0
    @Getter
    private enum YesOrNo {
        YES("1", "是"),
        NO("0", "否");
        private final String code;
        private final String value;
        YesOrNo(String code, String value) {
            this.code = code;
            this.value = value;
        }

        public static String getValueByCode(String code) {
            for (BusinessProcessPushRepairOrder.YesOrNo yesOrNo : BusinessProcessPushRepairOrder.YesOrNo.values()) {
                if (yesOrNo.getCode().equals(code)) {
                    return yesOrNo.getValue();
                }
            }
            return null;
        }
    }

    @Getter
    private enum PileBodyTypeEnum {
        PILE_BODY_TYPE_INSIDE("10", "桩体保内"),
        PILE_BODY_TYPE_OUTSIDE("20", "桩体保外"),
        PILE_BODY_TYPE_EXTENDED("30", "桩体延保");
        private final String code;
        private final String value;
        PileBodyTypeEnum(String code, String value) {
            this.code = code;
            this.value = value;
        }

        public static String getValueByCode(String code) {
            for (BusinessProcessPushRepairOrder.PileBodyTypeEnum pileBodyTypeEnum : BusinessProcessPushRepairOrder.PileBodyTypeEnum.values()) {
                if (pileBodyTypeEnum.getCode().equals(code)) {
                    return pileBodyTypeEnum.getValue();
                }
            }
            return null;
        }

    }
}
