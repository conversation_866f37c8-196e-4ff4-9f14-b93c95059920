package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/2/29 15:58
 */
@Data
public class PushRepairOrderData implements Serializable {

    /**
     * 安装订单编号
     */
    @ApiModelProperty(value = "安装订单编号", required = true)
    private String installOrderCode;

    /**
     * 安装联系人姓名
     */
    @ApiModelProperty(value = "安装联系人姓名", required = true)
    private String contactName;

    /**
     * 安装联系人手机号
     */
    @ApiModelProperty(value = "安装联系人手机号", required = true)
    private String contactMobile;

    /**
     * 安装地址: 详细地址信息(例如 XXX 社区 XX 小区)，不包含省市区的信息
     */
    @ApiModelProperty(value = "安装地址: 详细地址信息(例如 XXX 社区 XX 小区)，不包含省市区的信息", required = true)
    private String contactAddress;

    /**
     * 省编码
     */
    @ApiModelProperty(value = "省编码", required = true)
    private String provinceCode;

    /**
     * 省名称
     */
    @ApiModelProperty(value = "省名称", required = true)
    private String provinceName;

    /**
     * 市编码
     */
    @ApiModelProperty(value = "市编码")
    private String cityCode;

    /**
     * 市名称
     */
    @ApiModelProperty(value = "市名称")
    private String cityName;

    /**
     * 区编码
     */
    @ApiModelProperty(value = "区编码")
    private String areaCode;

    /**
     * 区名称
     */
    @ApiModelProperty(value = "区名称")
    private String areaName;

    /**
     * 充电桩名称
     */
    @ApiModelProperty(value = "充电桩名称", required = false)
    private String wallboxName;

    /**
     * 充电桩功率
     */
    @ApiModelProperty(value = "充电桩功率", required = true)
    private String wallboxPower;

    /**
     * 售后类型 10-报修，20-拆桩，30-移桩
     */
    @ApiModelProperty(value = "售后类型 10-报修，20-拆桩，30-移桩", required = true)
    private String requirmentType;

    /**
     * 派单时间 格式：yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "派单时间 格式：yyyy-MM-dd HH:mm:ss", required = true)
    private String dispatchTime;

    /**
     * 工程标签 10-保内订单，20-保外订单，30-延保订单
     */
    @ApiModelProperty(value = "工程标签 10-保内订单，20-保外订单，30-延保订单", required = true)
    private String type;

    /**
     * 桩体标签 pileBodyType 10-桩体保内，20-桩体保外，30-桩体延保
     */
    @ApiModelProperty(value = "桩体标签 pileBodyType 10-桩体保内，20-桩体保外，30-桩体延保", required = true)
    private String pileBodyType;

    /**
     * 品牌 10-比亚迪(c 端整体上线后会移除)，20-腾势，30-仰望，40- 海洋，50-王朝
     */
    @ApiModelProperty(value = "品牌 10-比亚迪(c 端整体上线后会移除)，20-腾势，30-仰望，40- 海洋，50-王朝，60-方程豹", required = true)
    private String carBrand;

    /**
     * 车系
     */
    @ApiModelProperty(value = "车系")
    private String carSeries;

    /**
     * 车型
     */
    @ApiModelProperty(value = "车型")
    private String carModel;

    /**
     * 车架号
     */
    @ApiModelProperty(value = "车架号")
    private String vin;

    /**
     * 故障原因
     */
    @ApiModelProperty(value = "故障原因")
    private String remark;

    /**
     * 报修订单编号
     */
    @ApiModelProperty(value = "报修订单编号", required = true)
    private String orderCode;

    /**
     * 移桩省编码
     */
    @ApiModelProperty(value = "移桩省编码")
    private String transferProvinceCode;
    /**
     * 移桩省名称
     */
    @ApiModelProperty(value = "移桩省名称")
    private String transferProvince;
    /**
     * 移桩市编码
     */
    @ApiModelProperty(value = "移桩市编码")
    private String transferCityCode;
    /**
     * 移桩市名称
     */
    @ApiModelProperty(value = "移桩市名称")
    private String transferCity;
    /**
     * 移桩区编码
     */
    @ApiModelProperty(value = "移桩区编码")
    private String transferAreaCode;
    /**
     * 移桩区名称
     */
    @ApiModelProperty(value = "移桩区名称")
    private String transferArea;
    /**
     * 移桩详细地址
     */
    @ApiModelProperty(value = "移桩详细地址")
    private String transferAddress;

    /**
     * 是否全国联保订单: 0-否 1-是
     */
    @ApiModelProperty(value = "是否全国联保订单: 0-否 1-是")
    private String globalGuarantee;

    /**
     * 是否可维护: 0-否 1-是
     */
    @ApiModelProperty(value = "是否可维护: 0-不可维护（该订单不支持回传），1-可维护")
    private String maintenanceStatus;

    /**
     * 移桩-拆桩和安装标志: 1-拆桩和安装，服务商需要回传移桩-拆桩和移桩-安装信息；2-拆桩，只回传移桩-拆桩信息；3-安装，只回传移桩-安装信息
     */
    @ApiModelProperty(value = "移桩-拆桩和安装标志", notes = "售后类型为移桩时，必填。1-拆桩和安装，服务商需要回传移桩-拆桩和移桩-安装信息；2-拆桩，只回传移桩-拆桩信息；3-安装，只回传移桩-安装信息")
    private String transferSplitInstallFlag;
}
